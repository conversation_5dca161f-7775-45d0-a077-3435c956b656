/* Modern CSS for Auto Shutdown Settings */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
}

.schedules-section, .settings-section {
    margin-bottom: 40px;
}

.schedules-list {
    display: grid;
    gap: 15px;
}

.schedule-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.schedule-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.schedule-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2d3748;
}

.schedule-info p {
    margin: 4px 0;
    color: #64748b;
    font-size: 0.9rem;
}

.schedule-status {
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.schedule-status.enabled {
    background: #dcfce7;
    color: #166534;
}

.schedule-status.disabled {
    background: #fee2e2;
    color: #991b1b;
}

.schedule-actions {
    display: flex;
    gap: 10px;
}

.no-schedules {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 40px;
}

.setting-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.setting-item label {
    font-weight: 500;
    color: #374151;
    min-width: 200px;
}

.setting-item input[type="number"] {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    width: 100px;
}

.setting-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 8px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 25px 50px rgba(0,0,0,0.25);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.close {
    color: #a0aec0;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #4a5568;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.form-group input[type="text"],
.form-group input[type="time"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.days-selector {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-top: 10px;
}

.day-checkbox {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px 5px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: white;
}

.day-checkbox:hover {
    border-color: #667eea;
    background: #f8fafc;
}

.day-checkbox input[type="checkbox"] {
    display: none;
}

.day-checkbox input[type="checkbox"]:checked + span {
    color: white;
}

.day-checkbox input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.day-checkbox:has(input:checked) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

.day-checkbox span {
    font-size: 0.9rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .schedule-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .schedule-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .days-selector {
        grid-template-columns: repeat(4, 1fr);
    }

    .actions {
        flex-direction: column;
    }
}
