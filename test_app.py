"""
Test script for Auto Shutdown Application
"""

import sys
import os
import json
from datetime import datetime, timedelta

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import Config<PERSON><PERSON>ger


def test_config_manager():
    """Test configuration management"""
    print("Testing Configuration Manager...")
    
    # Create test config manager
    config_manager = ConfigManager("test_config.json")
    
    # Test default config
    config = config_manager.load_config()
    print(f"Default config: {config}")
    
    # Test adding a schedule
    test_schedule = {
        "name": "Test Evening Shutdown",
        "time": "22:00",
        "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
        "enabled": True
    }
    
    config['schedules'].append(test_schedule)
    success = config_manager.save_config(config)
    print(f"Save config success: {success}")
    
    # Test loading saved config
    loaded_config = config_manager.load_config()
    print(f"Loaded config: {loaded_config}")
    
    # Clean up test file
    if os.path.exists("test_config.json"):
        os.remove("test_config.json")
    
    print("Configuration Manager test completed!\n")


def test_schedule_creation():
    """Test schedule creation and validation"""
    print("Testing Schedule Creation...")
    
    config_manager = ConfigManager("test_config.json")
    
    # Test multiple schedules
    schedules = [
        {
            "name": "Weekday Evening",
            "time": "22:00",
            "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
            "enabled": True
        },
        {
            "name": "Weekend Late",
            "time": "23:30",
            "days": ["saturday", "sunday"],
            "enabled": True
        },
        {
            "name": "Disabled Schedule",
            "time": "20:00",
            "days": ["monday"],
            "enabled": False
        }
    ]
    
    for schedule in schedules:
        success = config_manager.add_schedule(schedule)
        print(f"Added schedule '{schedule['name']}': {success}")
    
    # Test getting schedules
    all_schedules = config_manager.get_schedules()
    print(f"Total schedules: {len(all_schedules)}")
    
    for schedule in all_schedules:
        print(f"  - {schedule['name']} at {schedule['time']} on {', '.join(schedule['days'])} (Enabled: {schedule['enabled']})")
    
    # Clean up
    if os.path.exists("test_config.json"):
        os.remove("test_config.json")
    
    print("Schedule Creation test completed!\n")


def create_sample_config():
    """Create a sample configuration file for testing"""
    print("Creating sample configuration...")
    
    config_manager = ConfigManager("config.json")
    
    sample_config = {
        "schedules": [
            {
                "id": 1,
                "name": "Weekday Evening Shutdown",
                "time": "22:00",
                "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                "enabled": True
            },
            {
                "id": 2,
                "name": "Weekend Late Shutdown",
                "time": "23:30",
                "days": ["saturday", "sunday"],
                "enabled": True
            }
        ],
        "settings": {
            "countdown_duration": 30,
            "enable_notifications": True,
            "auto_start": False
        }
    }
    
    success = config_manager.save_config(sample_config)
    print(f"Sample config created: {success}")
    
    if success:
        print("Sample configuration saved to config.json")
        print("You can now run the main application!")
    
    return success


def main():
    """Run all tests"""
    print("Auto Shutdown Application - Test Suite")
    print("=" * 50)
    
    # Run tests
    test_config_manager()
    test_schedule_creation()
    
    # Create sample config
    create_sample_config()
    
    print("\nAll tests completed!")
    print("\nTo run the application:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Run the application: python main.py")
    print("3. Look for the system tray icon")
    print("4. Right-click the tray icon to access settings")


if __name__ == "__main__":
    main()
