"""
Auto Shutdown Application
A beautiful system tray application for scheduled shutdowns with slide-to-cancel interface
"""

import os
import sys
import json
import threading
import time
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

import webview
import pystray
from PIL import Image, ImageDraw
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import <PERSON>ronTrigger

from config_manager import ConfigManager
from shutdown_overlay import ShutdownOverlay


class AutoShutdownApp:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.scheduler = BackgroundScheduler()
        self.tray_icon = None
        self.settings_window = None
        self.shutdown_overlay = None
        self.is_running = True
        
        # Load configuration
        self.config = self.config_manager.load_config()
        
        # Initialize scheduler
        self.scheduler.start()
        self.setup_scheduled_shutdowns()
    
    def create_tray_icon(self):
        """Create system tray icon"""
        # Create a more sophisticated icon
        image = Image.new('RGBA', (64, 64), color=(0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        # Draw power symbol
        draw.ellipse([8, 8, 56, 56], outline='#667eea', width=4)
        draw.line([32, 16, 32, 32], fill='#667eea', width=4)

        menu = pystray.Menu(
            pystray.MenuItem("Settings", self.show_settings),
            pystray.MenuItem("View Schedules", self.show_schedules_info),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Test Shutdown", self.test_shutdown),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Exit", self.quit_app)
        )

        self.tray_icon = pystray.Icon("AutoShutdown", image, "Auto Shutdown", menu)
        return self.tray_icon
    
    def show_settings(self, icon=None, item=None):
        """Show settings window"""
        def create_settings_window():
            self.settings_window = webview.create_window(
                'Auto Shutdown Settings',
                'static/settings.html',
                width=800,
                height=600,
                resizable=True,
                js_api=self,
                minimized=False
            )
            webview.start(debug=False)

        # Run in separate thread to avoid blocking
        settings_thread = threading.Thread(target=create_settings_window, daemon=True)
        settings_thread.start()

    def show_schedules_info(self, icon=None, item=None):
        """Show quick info about active schedules"""
        schedules = self.config.get('schedules', [])
        active_schedules = [s for s in schedules if s.get('enabled', True)]

        if not active_schedules:
            message = "No active schedules configured."
        else:
            message = f"Active schedules ({len(active_schedules)}):\n"
            for schedule in active_schedules[:3]:  # Show max 3
                days = ', '.join(schedule.get('days', []))
                message += f"• {schedule.get('name', 'Unnamed')} - {schedule.get('time', 'N/A')} ({days})\n"
            if len(active_schedules) > 3:
                message += f"... and {len(active_schedules) - 3} more"

        # For now, print to console. In a real app, you might show a notification
        print(message)

    def test_shutdown(self, icon=None, item=None):
        """Test the shutdown overlay (for debugging)"""
        self.trigger_shutdown()
    
    def setup_scheduled_shutdowns(self):
        """Setup scheduled shutdowns based on configuration"""
        # Clear existing jobs
        self.scheduler.remove_all_jobs()
        
        for schedule_item in self.config.get('schedules', []):
            if schedule_item.get('enabled', True):
                self.add_scheduled_shutdown(schedule_item)
    
    def add_scheduled_shutdown(self, schedule_item):
        """Add a scheduled shutdown job"""
        days = schedule_item.get('days', [])
        time_str = schedule_item.get('time', '22:00')
        
        hour, minute = map(int, time_str.split(':'))
        
        # Convert day names to cron format
        day_mapping = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        cron_days = [str(day_mapping[day.lower()]) for day in days if day.lower() in day_mapping]
        
        if cron_days:
            trigger = CronTrigger(
                day_of_week=','.join(cron_days),
                hour=hour,
                minute=minute
            )
            
            self.scheduler.add_job(
                self.trigger_shutdown,
                trigger,
                id=f"shutdown_{schedule_item.get('id', 'default')}"
            )
    
    def trigger_shutdown(self):
        """Trigger shutdown with overlay"""
        print(f"Shutdown triggered at {datetime.now()}")

        def show_overlay():
            if self.shutdown_overlay is None:
                self.shutdown_overlay = ShutdownOverlay(
                    self.cancel_shutdown,
                    self.execute_shutdown,
                    self.config_manager
                )
            self.shutdown_overlay.show()

        # Run overlay in separate thread
        overlay_thread = threading.Thread(target=show_overlay, daemon=True)
        overlay_thread.start()

    def cancel_shutdown(self):
        """Cancel the shutdown"""
        print(f"Shutdown cancelled at {datetime.now()}")
        if self.shutdown_overlay:
            self.shutdown_overlay.hide()
            self.shutdown_overlay = None

    def execute_shutdown(self):
        """Execute system shutdown"""
        print(f"Executing shutdown at {datetime.now()}")
        try:
            # Windows shutdown command with immediate shutdown
            if sys.platform == "win32":
                subprocess.run(['shutdown', '/s', '/f', '/t', '0'], check=True)
            elif sys.platform in ["linux", "darwin"]:
                # For other platforms (testing)
                subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            else:
                print(f"Unsupported platform: {sys.platform}")
        except subprocess.CalledProcessError as e:
            print(f"Failed to shutdown: {e}")
        except FileNotFoundError:
            print("Shutdown command not found - running in test mode")
    
    def quit_app(self, icon=None, item=None):
        """Quit the application"""
        # Parameters are required by pystray but not used
        self.is_running = False
        if self.scheduler:
            self.scheduler.shutdown()
        if self.tray_icon:
            self.tray_icon.stop()
        # Close any open webview windows
        try:
            for window in webview.windows:
                window.destroy()
        except:
            pass
        sys.exit(0)
    
    # WebView API methods
    def get_config(self):
        """Get current configuration for web interface"""
        return self.config
    
    def save_config(self, new_config):
        """Save configuration from web interface"""
        self.config = new_config
        self.config_manager.save_config(self.config)
        self.setup_scheduled_shutdowns()
        return {"success": True}
    
    def run(self):
        """Run the application"""
        # Create and run tray icon in separate thread
        tray_thread = threading.Thread(target=self.run_tray, daemon=True)
        tray_thread.start()
        
        # Keep main thread alive
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.quit_app()
    
    def run_tray(self):
        """Run system tray"""
        icon = self.create_tray_icon()
        icon.run()


if __name__ == "__main__":
    app = AutoShutdownApp()
    app.run()
