"""
Shutdown Overlay with iPhone-style slide-to-unlock interface
"""

import threading
import time
import webview
from pathlib import Path


class ShutdownOverlay:
    def __init__(self, cancel_callback, shutdown_callback, config_manager=None):
        self.cancel_callback = cancel_callback
        self.shutdown_callback = shutdown_callback
        self.config_manager = config_manager
        self.window = None
        self.countdown_thread = None
        self.is_active = False
        self.countdown_duration = 30

        # Load countdown duration from config if available
        if self.config_manager:
            config = self.config_manager.load_config()
            self.countdown_duration = config.get('settings', {}).get('countdown_duration', 30)
    
    def show(self):
        """Show the shutdown overlay"""
        if self.is_active:
            return
        
        self.is_active = True
        
        # Create fullscreen overlay window
        self.window = webview.create_window(
            'Shutdown Warning',
            'static/shutdown_overlay.html',
            fullscreen=True,
            on_top=True,
            shadow=False,
            resizable=False,
            js_api=self
        )
        
        # Start countdown in separate thread
        self.countdown_thread = threading.Thread(target=self._countdown, daemon=True)
        self.countdown_thread.start()
        
        # Start webview if not already running
        if not webview.windows or len(webview.windows) == 1:
            webview.start(debug=False)
    
    def hide(self):
        """Hide the shutdown overlay"""
        self.is_active = False
        if self.window:
            try:
                self.window.destroy()
            except:
                pass
            self.window = None
    
    def _countdown(self):
        """Countdown timer"""
        for remaining in range(self.countdown_duration, 0, -1):
            if not self.is_active:
                return
            
            # Update countdown display
            if self.window:
                try:
                    self.window.evaluate_js(f'updateCountdown({remaining})')
                except:
                    pass
            
            time.sleep(1)
        
        # Time's up - execute shutdown
        if self.is_active:
            self.shutdown_callback()
    
    def cancel_shutdown(self):
        """Cancel shutdown (called from JavaScript)"""
        self.cancel_callback()
        return {"success": True}
    
    def get_countdown_duration(self):
        """Get countdown duration for JavaScript"""
        return self.countdown_duration
