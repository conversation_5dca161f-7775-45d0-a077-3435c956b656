/* Shutdown Overlay CSS with iPhone-style slide to unlock */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 0;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.overlay-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.background-blur {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    backdrop-filter: blur(20px);
    z-index: -1;
}

.warning-content {
    text-align: center;
    color: white;
    max-width: 500px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.warning-icon {
    margin-bottom: 20px;
    color: #fbbf24;
    animation: pulse 2s infinite;
}

.warning-icon svg {
    filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.3));
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

h1 {
    font-size: 2.2rem;
    font-weight: 300;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.warning-message {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.countdown-display {
    margin-bottom: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#countdown-timer {
    font-size: 4rem;
    font-weight: 200;
    color: #fbbf24;
    text-shadow: 0 4px 8px rgba(251, 191, 36, 0.3);
    display: block;
    line-height: 1;
}

.countdown-label {
    font-size: 1rem;
    opacity: 0.8;
    margin-top: 5px;
    display: block;
}

.slide-to-cancel-container {
    margin: 40px 0;
}

.slide-track {
    position: relative;
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    cursor: pointer;
}

.slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.slide-text {
    font-size: 1rem;
    font-weight: 500;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: opacity 0.3s ease, color 0.3s ease;
    pointer-events: none;
}

.slide-arrow {
    position: absolute;
    right: 20px;
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.6);
    animation: slideHint 2s infinite;
    pointer-events: none;
}

@keyframes slideHint {
    0%, 100% {
        transform: translateX(0);
        opacity: 0.6;
    }
    50% {
        transform: translateX(10px);
        opacity: 1;
    }
}

.slide-button {
    position: absolute;
    left: 4px;
    top: 4px;
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 26px;
    cursor: grab;
    transition: box-shadow 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.slide-button:active {
    cursor: grabbing;
}

.slide-button-inner {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    border-radius: 26px;
    transition: all 0.3s ease;
}

.slide-track.dragging .slide-button {
    transition: none;
}

.slide-track.dragging .slide-arrow {
    opacity: 0;
}

.additional-info {
    margin-top: 30px;
}

.additional-info p {
    margin-bottom: 10px;
    opacity: 0.8;
}

.small-text {
    font-size: 0.9rem;
    opacity: 0.6;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .warning-content {
        margin: 20px;
        padding: 30px 20px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    #countdown-timer {
        font-size: 3rem;
    }
    
    .slide-track {
        height: 50px;
    }
    
    .slide-button {
        width: 42px;
        height: 42px;
        border-radius: 21px;
    }
    
    .slide-button-inner {
        border-radius: 21px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .overlay-container {
        background: #000000;
    }
    
    .warning-content {
        background: rgba(255, 255, 255, 0.95);
        color: #000000;
        border: 2px solid #ffffff;
    }
    
    .slide-track {
        background: #333333;
        border: 2px solid #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .warning-content {
        animation: none;
    }
    
    .warning-icon {
        animation: none;
    }
    
    .slide-arrow {
        animation: none;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }
}
